import { Button } from "./ui/button";

export default function Navbar() {
    return (
        <nav className="fixed top-0 left-0 right-0 p-4">
            <div className="container mx-auto flex justify-between items-center">
                <div className="flex justify-center items-center cursor-target">
                    <img src="/logo_white.png" alt="Logo" height={36} width={36} />
                    <a href="/" className="font-bold text-lg pr-1">&gt;ssh occo.rocks</a>
                </div>
                <div className="flex items-center gap-4">
                    <Button variant="ghost" asChild><a href="/about">About</a></Button>
                    <Button variant="ghost" asChild><a href="/projects">Projects</a></Button>
                    <Button variant="default" asChild><a href="/contact">Contact</a></Button>
                </div>
            </div>
        </nav>
    );
}
