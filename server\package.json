{"name": "server", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "bun --watch run src/index.ts && tsc --watch", "db:migrate": "drizzle-kit migrate", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@t3-oss/env-core": "^0.13.8", "drizzle-orm": "^0.44.3", "hono": "^4.7.11", "pg": "^8.16.3", "postgres": "^3.4.4", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest", "@types/pg": "^8.15.4", "drizzle-kit": "^0.31.4"}}