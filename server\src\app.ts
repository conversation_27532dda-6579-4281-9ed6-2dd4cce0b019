import { Hono } from "hono";
import { serveStatic } from "hono/bun";
import { cors } from "hono/cors";
import {
    type ApiResponse,
    type DiscordApiResponse,
    type DiscordUser,
    generateId,
    type Message,
} from "shared/dist";
import { db } from "@server/db";
import { messages } from "@server/db/schema";

export const app = new Hono();
export const apiBase = new Hono();

apiBase.post("/send", async (c) => {
        const post = await c.req.json<Message>();
        if (!post) {
            return c.json(
                { error: "You can't send an empty message, stupid." },
                { status: 400 },
            );
        }
        const [saveMessage] = await db
            .insert(messages)
            .values({
                id: generateId(32),
                message: post.message,
                createdAt: new Date(),
            })
            .returning();
        if (!saveMessage) {
            return c.json({ error: "Failed to save message." }, { status: 500 });
        }
        const data: ApiResponse = {
            success: true,
            message:
                "I might add a reply feature soon, should I be normal or weird about it... hmmm...",
        };

        return c.json({ data }, { status: 200 });
    })
    .get("/discord", async (c) => {
        try {
            const response = await fetch(
                "http://192.168.0.48:4001/v1/users/1383592267201384469",
            );
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }
            const discordData = (await response.json()) as DiscordApiResponse;
            const data: DiscordApiResponse = {
                ...discordData
            }

            return c.json(data, { status: 200 });
        } catch (error) {
            return c.json({ error: "Failed to fetch Discord data" }, { status: 500 });
        }
    });

app.use(cors());

app.use("*", serveStatic({ root: "./static" }));

app.use("*", async (c, next) => {
    return serveStatic({ root: "./static", path: "index.html" })(c, next);
});

app.route("/api", apiBase);

const port = parseInt(process.env.PORT || "3000");

export default {
    port,
    fetch: app.fetch,
};

console.log(`🦫 bhvr server running on port ${port}`);
