import { useForm } from "@tanstack/react-form";
import { createFileRoute } from "@tanstack/react-router";
import { LoaderCircle, Send } from "lucide-react";
import z from "zod";
import Cursor from "@/components/cursor";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { ApiResponse } from "shared";

export const Route = createFileRoute("/")({
  component: Home,
});

function Home() {
  const form = useForm({
    defaultValues: {
      message: "",
    },
    validators: {
      onChange: z.object({
        message: z.string(),
      }),
    },
    onSubmit: async ({ value, formApi }) => {
      const res = await fetch("/api/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(value),
      });
      const data = await res.json() as ApiResponse;
      if (!data.success) {
        throw new Error(
          "Unable to send a message - Retry but if it's consistent, contact me.",
        );
      }
      formApi.reset();
    },
  });

  return (
    <div className="min-h-screen bg-background text-white font-primary overflow-hidden relative w-full">
      <Cursor />
      <main className="container mx-auto text-center flex flex-col items-center justify-center min-h-[calc(100vh-4rem)]">
        <h1 className="text-6xl font-bold">
          hi, i'm <span className="cursor-target">occorune.</span>
          <br />
          just another developer.
        </h1>
        <p className="mt-4 text-lg text-white/75">
          all my github projects be private like fuck
        </p>
        <div className="flex flex-col mt-4 gap-4">
          <h1>wanna say or send me something? go for it.</h1>
          <form
            className="flex flex-row gap-2"
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              void form.handleSubmit();
            }}
          >
            <form.Field
              name="message"
              validators={{
                onChange: ({ value }) =>
                  !value
                    ? "A message is required to submit this, silly."
                    : value.length < 4
                      ? "This should be above 4 characters."
                      : undefined,
                onChangeAsyncDebounceMs: 500,
              }}
            >
              {(field) => (
                <Input
                  id={field.name}
                  name={field.name}
                  placeholder="just try not spam it xoxo"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  field={field}
                />
              )}
            </form.Field>

            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              // biome-ignore lint/correctness/noChildrenProp: This is how it works
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" variant="default" disabled={!canSubmit}>
                  {isSubmitting ? (
                    <>
                      Sending{" "}
                      <LoaderCircle className="inline w-4 h-4 ml-1 animate-spin" />
                    </>
                  ) : (
                    <>
                      Get it sent <Send className="inline w-4 h-4 ml-1" />
                    </>
                  )}
                </Button>
              )}
            />
          </form>
        </div>
      </main>
    </div>
  );
}
